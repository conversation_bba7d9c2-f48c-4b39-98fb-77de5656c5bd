import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class EditAccountScreen extends StatefulWidget {
  final Map<String, dynamic> account;

  const EditAccountScreen({super.key, required this.account});

  @override
  State<EditAccountScreen> createState() => _EditAccountScreenState();
}

class _EditAccountScreenState extends State<EditAccountScreen> {
  final _formKey = GlobalKey<FormState>();
  final _bankNameController = TextEditingController();
  final _nicknameController = TextEditingController();
  final _balanceController = TextEditingController();

  String _selectedAccountType = 'Conta Corrente';
  bool _isActive = true;

  final List<String> _accountTypes = ['Conta Corrente', 'Conta Poupança'];

  @override
  void initState() {
    super.initState();
    _loadAccountData();
  }

  void _loadAccountData() {
    _bankNameController.text = widget.account['nomeBanco'] ?? '';
    _nicknameController.text = widget.account['apelido'] ?? '';
    _balanceController.text = (widget.account['saldo'] ?? 0.0).toString();
    _selectedAccountType = widget.account['tipoConta'] ?? 'Conta Corrente';
    _isActive = widget.account['ativa'] ?? true;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Editar Conta',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header informativo
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: const Color(0xFF16213E),
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: Colors.orange.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(Icons.edit, color: Colors.orange, size: 24),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        'Edite as informações da sua conta',
                        style: TextStyle(color: Colors.white70),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 30),

              // Nome do banco
              _buildTextField(
                controller: _bankNameController,
                label: 'Nome do Banco/Instituição',
                hint: 'Ex: Nubank, Itaú, Bradesco...',
                icon: Icons.business,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Nome do banco é obrigatório';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),

              // Tipo de conta
              _buildDropdownField(),
              const SizedBox(height: 20),

              // Apelido
              _buildTextField(
                controller: _nicknameController,
                label: 'Apelido',
                hint: 'Ex: Conta Principal, Conta Salário...',
                icon: Icons.label,
              ),
              const SizedBox(height: 20),

              // Saldo atual
              _buildTextField(
                controller: _balanceController,
                label: 'Saldo Atual',
                hint: '0,00',
                icon: Icons.attach_money,
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9.,]')),
                ],
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    final amount = double.tryParse(value.replaceAll(',', '.'));
                    if (amount == null) {
                      return 'Valor inválido';
                    }
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),

              // Status da conta
              _buildStatusToggle(),
              const SizedBox(height: 30),

              // Preview da conta
              _buildAccountPreview(),
              const SizedBox(height: 30),

              // Botões de ação
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: Colors.white54),
                        padding: const EdgeInsets.symmetric(vertical: 15),
                      ),
                      child: const Text(
                        'Cancelar',
                        style: TextStyle(color: Colors.white54),
                      ),
                    ),
                  ),
                  const SizedBox(width: 15),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _saveAccount,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        padding: const EdgeInsets.symmetric(vertical: 15),
                      ),
                      child: const Text(
                        'Salvar Alterações',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          validator: validator,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: const TextStyle(color: Colors.white54),
            prefixIcon: Icon(icon, color: Colors.white54),
            filled: true,
            fillColor: const Color(0xFF16213E),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.orange),
            ),
          ),
          onChanged: (value) => setState(() {}),
        ),
      ],
    );
  }

  Widget _buildDropdownField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Tipo de Conta',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _selectedAccountType,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            prefixIcon: const Icon(Icons.category, color: Colors.white54),
            filled: true,
            fillColor: const Color(0xFF16213E),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.orange),
            ),
          ),
          dropdownColor: const Color(0xFF16213E),
          items: _accountTypes.map((String type) {
            return DropdownMenuItem<String>(
              value: type,
              child: Text(type, style: const TextStyle(color: Colors.white)),
            );
          }).toList(),
          onChanged: (String? newValue) {
            if (newValue != null) {
              setState(() {
                _selectedAccountType = newValue;
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildStatusToggle() {
    return Row(
      children: [
        Switch(
          value: _isActive,
          onChanged: (value) => setState(() => _isActive = value),
          activeColor: Colors.green,
          inactiveThumbColor: Colors.red,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _isActive ? 'Conta Ativa' : 'Conta Inativa',
                style: TextStyle(
                  color: _isActive ? Colors.green : Colors.red,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                _isActive
                    ? 'A conta aparecerá no dashboard'
                    : 'A conta ficará oculta no dashboard',
                style: const TextStyle(color: Colors.white70, fontSize: 12),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAccountPreview() {
    final bankName = _bankNameController.text.isEmpty
        ? 'Nome do Banco'
        : _bankNameController.text;
    final nickname = _nicknameController.text;
    final balance =
        double.tryParse(_balanceController.text.replaceAll(',', '.')) ?? 0.0;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: _isActive
              ? [Colors.orange, Colors.orange.withValues(alpha: 0.8)]
              : [Colors.grey.shade600, Colors.grey.shade700],
        ),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    nickname.isNotEmpty ? nickname : bankName,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    _selectedAccountType,
                    style: const TextStyle(color: Colors.white70, fontSize: 14),
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: _isActive ? Colors.green : Colors.red,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _isActive ? 'Ativa' : 'Inativa',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          const Text(
            'Saldo Atual',
            style: TextStyle(color: Colors.white70, fontSize: 14),
          ),
          Text(
            'R\$ ${balance.toStringAsFixed(2)}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 28,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  void _saveAccount() {
    if (_formKey.currentState!.validate()) {
      final updatedAccount = {
        ...widget.account,
        'nomeBanco': _bankNameController.text,
        'tipoConta': _selectedAccountType,
        'apelido': _nicknameController.text,
        'saldo':
            double.tryParse(_balanceController.text.replaceAll(',', '.')) ??
            0.0,
        'ativa': _isActive,
        'dataUltimaAtualizacao': DateTime.now(),
      };

      Navigator.pop(context, updatedAccount);

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Conta atualizada com sucesso!'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
}
